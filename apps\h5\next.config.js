/* eslint-disable */
const createNextIntlPlugin = require('next-intl/plugin')
// const gitCommitHash = require('child_process').execSync('git rev-parse HEAD').toString().trim();
// const pkg = require('./package.json')

const isProduction = process.env.NODE_ENV === 'production'
const timeStamp = Date.now()

// 增加 i18n 插件支持
const withNextIntl = createNextIntlPlugin()

/* ------------------------------ secure image ------------------------------ */
const SECURE_IMAGES_DOMAIN = process.env.SECURE_IMAGES_DOMAIN
  ? process.env.SECURE_IMAGES_DOMAIN.split(',')
  : []

const remotePatternsImage = SECURE_IMAGES_DOMAIN.map((path) => {
  const url = new URL(path)
  return {
    protocol: url.protocol.slice(0, -1),
    hostname: url.hostname,
    port: url.port,
  }
})
/* ---------------------------- end secure image ---------------------------- */

/** @type {import('next').NextConfig} */
const nextConfig = {
  assetPrefix: isProduction ? process.env.NEXT_PUBLIC_CDN_URL || undefined : undefined,
  trailingSlash: false,
  poweredByHeader: !isProduction,
  generateEtags: false,
  generateBuildId: async () => {
    return `${timeStamp}`
  },
  // 开启 source map
  productionBrowserSourceMaps: !isProduction,
  // distDir: isProduction ? `.next_${timeStamp}` : '.next',
  images: {
    remotePatterns: remotePatternsImage,
    unoptimized: true,
  },
  webpack: (config, { buildId, dev, webpack, isServer }) => {
    if (!dev) {
      config.plugins.push(
        new webpack.BannerPlugin({
          banner: `BUILD_VERSION: ${buildId}`,
        }),
      )

      config.plugins.push(
        new webpack.DefinePlugin({
          'process.env.BUILD_VERSION': buildId,
        }),
      )
    }

    // 给所有静态资源文件路径添加 buildId
    if (!dev && !isServer) {
      // 主要 bundle 文件
      config.output.filename = `static/js/[name].[contenthash]-${buildId}.js`
      // chunk 文件
      config.output.chunkFilename = `static/js/[name].[contenthash]-${buildId}.js`

      // 处理CSS文件
      const miniCssExtractPlugin = config.plugins.find(
        (plugin) => plugin.constructor.name === 'NextMiniCssExtractPlugin',
      )
      if (miniCssExtractPlugin) {
        miniCssExtractPlugin.options.filename = `static/css/[contenthash]-${buildId}.css`
        miniCssExtractPlugin.options.chunkFilename = `static/css/[contenthash]-${buildId}.css`
      }
    }

    return config
  },
  experimental: {
    serverActions: {
      allowedOrigins: isProduction ? [process.env.NEXT_PUBLIC_SHOP_API_URL || ''] : [],
    },
  },
  transpilePackages: ['@ninebot/core', '@reduxjs/toolkit'],
  // logging: {
  //   fetches: {
  //     fullUrl: true,
  //   },
  // },
}

module.exports = withNextIntl(nextConfig)
